"use client";

import { usePairContext } from "@/app/trade/[symbol]/provider";
import { AppButton } from "@/components/AppButton";
import AppNumber from "@/components/AppNumber";
import { EOrderType } from "@/components/OrderForm";
import { RootState } from "@/store";
import { EOrderStatus, TOrderHistory } from "@/types/order";
import { formatUnixTimestamp } from "@/utils/format";
import {
  calculateFilledPercent,
  getSideColor,
  getTriggerConditionDisplay,
} from "@/utils/helper";
import BigNumber from "bignumber.js";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";

export const OrderHistoryItem = ({ order }: { order: TOrderHistory }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const pairSettings = useSelector(
    (state: RootState) => state.pairSettings.pairSettings
  );

  const pairSetting = useMemo(() => {
    if (!pairSettings.length) {
      return {} as any;
    }

    return pairSettings?.find(
      (setting) => setting.symbol.toUpperCase() === order.symbol.toUpperCase()
    );
  }, [pairSettings]);

  const getDisplayPrice = (order: TOrderHistory) => {
    return order.type === EOrderType.LIMIT ? order.price : order.stop_price;
  };

  const getOrderTypeDisplay = (type: EOrderType) => {
    switch (type) {
      case EOrderType.LIMIT:
        return "Limit";
      case EOrderType.MARKET:
        return "Market";
      case EOrderType.STOP_LIMIT:
        return "Stop Limit";
      case EOrderType.STOP_MARKET:
        return "Stop Market";
      default:
        return type;
    }
  };

  const getAvgPrice = (order: TOrderHistory) => {
    if (!order.executed_qty) {
      return "0";
    }

    const price = BigNumber(order.cummulative_quote_qty || 0)
      .dividedBy(order.executed_qty)
      .toFixed();

    return price;
  };

  if (isMobile) {
    return (
      <div className="border-white-50 border-b px-4 py-2">
        <div className="flex items-center justify-between">
          <div>
            <div className="body-md-medium-14">
              {order?.symbol?.toUpperCase()}
            </div>
            <div className="body-sm-regular-12 flex gap-2 capitalize">
              <div
                style={{ color: getSideColor(order?.side) }}
              >{`${getOrderTypeDisplay(
                order?.type
              )}/${order?.side?.toLowerCase()}`}</div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="body-md-regular-14 text-white-500">
              {" "}
              {calculateFilledPercent(order.executed_qty, order.orig_qty)}%
            </div>
            <AppButton
              variant="secondary"
              size="small"
              className="px-2 !text-[10px]"
            >
              Cancel
            </AppButton>
          </div>
        </div>

        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Time</div>
          <div className="body-md-medium-14">
            {formatUnixTimestamp(order?.created_at, "YYYY-MM-DD HH:mm:ss")}
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Price</div>
          <div className="body-md-medium-14">
            {" "}
            {[EOrderType.STOP_MARKET, EOrderType.MARKET].includes(
              order.type
            ) ? (
              "Market"
            ) : (
              <AppNumber
                value={getDisplayPrice(order) || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            )}
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Amount</div>
          <div className="body-md-medium-14">
            <AppNumber
              value={order?.executed_qty || 0}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Total</div>
          <div className="body-md-medium-14">
            <AppNumber
              value={order.cummulative_quote_qty || "0"}
              decimals={8} // Would be 8 for total
              isFormatLargeNumber={false}
            />
          </div>
        </div>
        <div className="flex items-center justify-between py-3">
          <div className="body-md-regular-14 text-white-500">Filled</div>
          <div className="body-md-medium-14">
            <AppNumber
              value={order?.executed_qty || "0"}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
        {(order?.type === EOrderType.STOP_LIMIT ||
          order?.type === EOrderType.STOP_MARKET) && (
          <div className="flex items-center justify-between py-3">
            <div className="body-md-regular-14 text-white-500">
              Trigger Condition
            </div>
            <div className="body-md-medium-14 flex gap-1">
              <span>{getTriggerConditionDisplay(order)}</span>
              {order.stop_price && (
                <AppNumber
                  value={order.stop_price || "0"}
                  decimals={pairSetting?.pricePrecision}
                  isFormatLargeNumber={false}
                />
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`border-white-50 hover:bg-white-50 flex w-full items-center border-b ${
        [EOrderStatus.CANCELLED, EOrderStatus.REJECTED].includes(order.status)
          ? "opacity-50"
          : ""
      }`}
    >
      <div className="body-sm-regular-12 text-white-1000 flex w-[10%] min-w-[90px] items-center px-2 py-2.5 ">
        {formatUnixTimestamp(order?.created_at, "MM-DD HH:mm:ss")}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        {order?.symbol?.toUpperCase()}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        {getOrderTypeDisplay(order?.type)}
      </div>
      <div
        className="body-sm-regular-12 w-[9%] min-w-[70px] px-2 py-2.5 text-left capitalize"
        style={{ color: getSideColor(order?.side) }}
      >
        {order?.side?.toLowerCase()}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        <AppNumber
          value={getAvgPrice(order)}
          decimals={pairSetting?.pricePrecision}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        {" "}
        {[EOrderType.STOP_MARKET, EOrderType.MARKET].includes(order.type) ? (
          "Market"
        ) : (
          <AppNumber
            value={getDisplayPrice(order) || 0}
            decimals={pairSetting?.pricePrecision}
            isFormatLargeNumber={false}
          />
        )}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        <AppNumber
          value={order?.executed_qty || "0"}
          decimals={pairSetting?.quantityPrecision}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12  text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left">
        <AppNumber
          value={order?.executed_qty || "0"}
          decimals={pairSetting?.quantityPrecision}
          isFormatLargeNumber={false}
        />
      </div>
      <div className="body-sm-regular-12 text-white-1000 flex w-[9%] min-w-[80px] items-center gap-1 px-2 py-2.5 text-left">
        <AppNumber
          value={order?.cummulative_quote_qty || "0"}
          decimals={8}
          isFormatLargeNumber={false}
        />{" "}
        {pairSetting?.quoteAsset?.toUpperCase()}
      </div>
      <div className="body-sm-regular-12 text-white-1000 w-[9%] min-w-[80px] px-2 py-2.5 text-left capitalize">
        {String(order?.status).toLowerCase()}
      </div>
      <div className="body-sm-regular-12 text-white-1000 flex w-[9%] min-w-[120px] items-center justify-center gap-1 px-2 py-2.5 text-left">
        <span>{getTriggerConditionDisplay(order)}</span>

        {order.stop_price && (
          <AppNumber
            value={order?.stop_price || "0"}
            decimals={pairSetting?.pricePrecision}
            isFormatLargeNumber={false}
          />
        )}
      </div>
    </div>
  );
};

OrderHistoryItem.displayName = "OrderHistoryItem";
