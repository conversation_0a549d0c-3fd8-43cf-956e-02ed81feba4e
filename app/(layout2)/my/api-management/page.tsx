"use client";

import React, { useEffect, useState } from "react";
import { ChevronDownIcon, CheckboxCheckedIcon } from "@/assets/icons";
import { APIItem } from "./components/APIItem";
import { ModalConfirmDefaultSecurityControls } from "@/modals";
import rf from "@/services/RequestFactory";
import { NotFoundIcon } from "@/assets/icons";
import { ButtonCreateApiKey } from "./components/ButtonCreateApiKey";
import { TApiKey } from "@/types/apiKey";

const ApiManagementPage = () => {
  const [isShowFullSecurityControls, setIsShowFullSecurityControls] =
    useState<boolean>(false);
  const [
    isShowModalConfirmDefaultSecurityControls,
    setIsShowModalConfirmDefaultSecurityControls,
  ] = useState<boolean>(false);

  const [isMounted, setIsMounted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [apiKeys, setApiKeys] = useState<TApiKey[]>([]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const getApiKeys = async () => {
    try {
      setIsLoading(true);
      const res = await rf.getRequest("ApiManagementRequest").getApiKey();
      setIsLoading(false);
      setApiKeys(res);
    } catch (e: any) {
      setIsLoading(false);
      console.error(e.message, "Get Api Keys Error");
    }
  };

  useEffect(() => {
    getApiKeys().then();
  }, []);

  if (!isMounted) return <></>;

  return (
    <div className="px-4 py-6 lg:px-0 lg:py-0">
      <div className="flex flex-col justify-between gap-4 lg:flex-row">
        <div className="heading-lg-medium-24 lg:text-white-500">
          API Management
        </div>
        <div className="flex gap-2 lg:gap-4">
          <ButtonCreateApiKey fetchData={getApiKeys} />
        </div>
      </div>

      <div className="body-sm-regular-12 text-white-500 mt-6">
        <div>1. Each account can create up to 30</div>
        <div>
          2. API Keys Do not disclose your API Key, Secret Key (HMAC) or Private
          Key (Ed25519, RSA) to anyone to avoid asset losses. You should treat
          your API Key and your Secret Key (HMAC) or Private Key (Ed25519, RSA)
          like your passwords.
        </div>
        <div>
          3. It is recommended to restrict access to trusted IPs only to
          increase your account security
        </div>
        <div>
          4. You will not be to able to create an API Key if KYC is not
          completed
        </div>
      </div>

      {/* <div className="bg-white-100 mb-8 mt-4 flex gap-2 rounded-[8px] px-2 py-4">
        <div
          className="cursor-pointer"
          onClick={() => setIsShowModalConfirmDefaultSecurityControls(true)}
        >
          <CheckboxCheckedIcon />
        </div>

        <div>
          <div className="body-md-regular-14">
            By checking this box, all existing API Key(s) on your master account
            and sub-accounts will be subject to Default Security Controls.
          </div>
          <div
            className="text-brand-500 body-md-regular-14 mt-2 flex cursor-pointer items-center gap-2"
            onClick={() =>
              setIsShowFullSecurityControls(!isShowFullSecurityControls)
            }
          >
            Default Security Controls Details{" "}
            <ChevronDownIcon
              className={isShowFullSecurityControls ? "rotate-[-180deg]" : ""}
            />
          </div>

          {isShowFullSecurityControls && (
            <div className="body-sm-regular-12 mt-2">
              We strongly recommend against enabling API key(s) for additional
              permissions (other than reading) without defining the appropriate
              IP access restrictions (“Unrestricted-IP-Access API Key(s)”).
              Enabling such Unrestricted-IP-Access API Key(s) would cause an
              increased risk of unauthorized access to your VDAX Account. <br />{" "}
              <br /> By default, the following security controls (&quot;Default
              Security Controls&quot;) would apply (including but not limited
              to): <br /> Symmetric HMAC Unrestricted-IP-Access API Key(s)
              being:
              <br /> prevented from enabling trading or transfer permissions;
              and <br /> periodically or immediately revoked (or certain
              permissions revoked) if previously enabled with trading or
              transfer permissions.
              <br /> Asymmetric Ed25519 and RSA Unrestricted-IP-Access API
              Key(s) being:
              <br /> periodically or immediately revoked (or certain permissions
              revoked) if enabled with trading or transfer permissions.
              <br /> For the avoidance of doubt, the Default Security Controls
              may, at the sole discretion of VDAX, be subject to change without
              prior notification.
              <br /> <br /> Please be reminded that you should not disclose or
              share your API key(s). Disclosing your API key(s) and other access
              credentials could lead to your VDAX Account being compromised. You
              are solely responsible for taking the necessary security measures
              to protect your VDAX Account, your API key(s), access credentials
              and personal information.
            </div>
          )}
        </div>
      </div> */}

      {isLoading ? (
        <div className="flex h-[400px] flex-1 flex-col items-center justify-center lg:mt-0">
          <div className="body-md-regular-14">Loading...</div>
        </div>
      ) : (
        <>
          {!!apiKeys.length ? (
            apiKeys.map((item, index: number) => {
              return (
                <APIItem key={index} fetchData={getApiKeys} apiKey={item} />
              );
            })
          ) : (
            <div className="flex h-[400px] flex-1 flex-col items-center justify-center lg:mt-0">
              <NotFoundIcon />
              <div className="text-white-500 body-md-regular-14">
                Your Account has not <br /> created any API Keys yet.
              </div>
            </div>
          )}
        </>
      )}

      {isShowModalConfirmDefaultSecurityControls && (
        <ModalConfirmDefaultSecurityControls
          isOpen={isShowModalConfirmDefaultSecurityControls}
          onClose={() => setIsShowModalConfirmDefaultSecurityControls(false)}
        />
      )}
    </div>
  );
};

export default ApiManagementPage;
