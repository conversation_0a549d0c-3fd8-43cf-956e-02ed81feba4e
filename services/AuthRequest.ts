import config from "@/config";
import { TUpdateReferrerParams } from "@/types/account";
import {
  TLoginParams,
  TRegisterParams,
  TResendVerificationCodeParams,
  TResetPasswordParams,
  TTwoFAVerifyParams,
  TVerifyCodeParams,
} from "@/types/auth";
import BaseRootRequest from "./BaseRequest";

export default class AuthRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.appApiUrl;
  }

  register({ email, password }: TRegisterParams) {
    const url = `/api/register`;
    return this.post(url, { email, password });
  }

  resendVerificationCode({ email }: TResendVerificationCodeParams) {
    const url = `/api/resend-verification-code`;
    return this.post(url, { email });
  }

  verifyCode({ email, code }: TVerifyCodeParams) {
    const url = `/api/verify-code`;
    return this.post(url, { email, code });
  }

  login({ email, password, mfaCode }: TLoginParams) {
    const url = `/api/login`;
    return this.post(url, { email, password, mfaCode });
  }

  logout() {
    const url = `/api/logout`;
    return this.get(url);
  }

  requestEmailCodeToEnable2FA() {
    const url = "/api/two-fa";
    return this.post(url, { action: "request-enable" });
  }

  requestEmailCodeToDisable2FA() {
    const url = "/api/two-fa";
    return this.post(url, { action: "request-disable" });
  }

  resendEmail() {
    const url = "/api/two-fa";
    return this.post(url, { action: "resend-enable-code" });
  }

  verifyAuthenticatorCode(data?: TTwoFAVerifyParams) {
    const url = "/api/two-fa";
    return this.post(url, { action: "verify-authenticator-code", ...data });
  }

  verifyDisableCode(data?: TTwoFAVerifyParams) {
    const url = "/api/two-fa";
    return this.post(url, { action: "verify-disable-code", ...data });
  }

  verifyEnableCode(data?: TTwoFAVerifyParams) {
    const url = "/api/two-fa";
    return this.post(url, { action: "verify-enable-code", ...data });
  }

  requestDisableAccount() {
    const url = "/api/account";
    return this.post(url, { action: "request-disable" });
  }

  requestDeleteAccount() {
    const url = "/api/account";
    return this.post(url, { action: "request-delete" });
  }

  verifyDisableAccount(data?: TTwoFAVerifyParams) {
    const url = "/api/account";
    return this.post(url, { action: "verify-disable", ...data });
  }

  verifyDeleteAccount(data?: TTwoFAVerifyParams) {
    const url = "/api/account";
    return this.post(url, { action: "verify-delete", ...data });
  }

  updateReferrerCode(data?: TUpdateReferrerParams) {
    const url = "/api/account";
    return this.post(url, { action: "referrer-code", ...data });
  }

  checkNewUser() {
    const url = "/api/check-new-user";
    return this.get(url);
  }

  clearNewUserCookie() {
    const url = "/api/clear-new-user-cookie";
    return this.post(url);
  }

  requestForgotPassword(email: string) {
    const url = "/api/forgot-password";
    return this.post(url, { email });
  }

  resetPassword({ confirmPassword, password, token }: TResetPasswordParams) {
    const url = "/api/reset-password";
    return this.post(url, { confirmPassword, password, token });
  }
}
