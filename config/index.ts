import dev from "./dev.json";
import uat from "./uat.json";
import local from "./local.json";
import staging from "./staging.json";

export interface Config {
  apiUrl: string;
  apiMarketDataUrl: string;
  appApiUrl: string;
  googleCallbackPath: string;
  wsUrl: string;
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || "local";

interface EnvConfig {
  uat: Config;
  dev: Config;
  staging: Config;
  local: Config;
}

const configs: EnvConfig = { dev, uat, local, staging } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
