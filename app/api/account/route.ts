import config from "@/config";
import { generateHMAC } from "@/utils/auth";
import axios, { AxiosError } from "axios";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants";

const AUTH_URL = config.apiUrl;
const secretKey = process.env.APP_SECRET_KEY || "";

// Map action to backend endpoint
const ACTION_ENDPOINT_MAP: Record<string, string> = {
  "request-disable": "/v1/account/request-disable",
  "request-delete": "/v1/account/request-delete",
  "verify-disable": "/v1/account/verify-disable",
  "verify-delete": "/v1/account/verify-delete",
  "referrer-code": "/v1/referrer-code",
};

export async function POST(request: Request) {
  const { action, ...otherData } = await request.json();

  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIES_ACCESS_TOKEN_KEY)?.value;

    if (!action) {
      return NextResponse.json(
        { success: false, error: "Action parameter is required" },
        { status: 400 }
      );
    }

    const endpoint = ACTION_ENDPOINT_MAP[action];
    if (!endpoint) {
      return NextResponse.json(
        { success: false, error: `Invalid action: ${action}` },
        { status: 400 }
      );
    }

    const timestamp = Date.now();

    // Create data object for HMAC generation
    const dataForHMAC = {
      ...otherData,
      timestamp,
    };

    const signature = generateHMAC(dataForHMAC, secretKey);

    // Prepare headers
    const headers: Record<string, string> = {
      "X-TIMESTAMP": timestamp.toString(),
      "X-SIGNATURE": signature,
    };

    // Add Authorization header if token exists
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    console.log(`Account API call - action: ${action}`, otherData);
    let response: { data: any };

    if (["request-disable", "request-delete"].includes(action)) {
      response = await axios.delete(`${AUTH_URL}${endpoint}`, {
        headers,
      });
    } else {
      response = await axios.post(`${AUTH_URL}${endpoint}`, otherData, {
        headers,
      });
    }

    return NextResponse.json({ success: true, data: response.data });
  } catch (error: unknown) {
    console.log(`Account API error - action ${action}`, error);
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      const status = axiosError.response?.status || 500;
      const message =
        (axiosError.response?.data as { message?: string })?.message ||
        axiosError.message ||
        "Unexpected error";

      return NextResponse.json({ success: false, error: message }, { status });
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
