"use client";

import React, { useState, useEffect } from "react";
import { AppButton } from "@/components";
import { EyeIcon, EyeCloseIcon } from "@/assets/icons";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

export default function ResetPasswordPage() {
  const [code, setCode] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);
  const [errors, setErrors] = useState({
    code: "",
    password: "",
    confirmPassword: "",
  });

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get email and code from URL parameters if available
    const codeParam = searchParams?.get("code");

    if (codeParam) {
      setCode(codeParam);
    }
  }, [searchParams]);

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case "code":
        setCode(value);
        break;
      case "password":
        setPassword(value);
        break;
      case "confirmPassword":
        setConfirmPassword(value);
        break;
    }

    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      email: "",
      code: "",
      password: "",
      confirmPassword: "",
    };

    if (!code.trim()) {
      newErrors.code = "Please enter the verification code.";
    } else if (code.length !== 8) {
      newErrors.code = "Verification code must be 8 characters long.";
    }

    if (!password.trim()) {
      newErrors.password = "Please enter a new password.";
    } else if (password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long.";
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = "Please confirm your password.";
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match.";
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error !== "");
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await rf.getRequest("AuthRequest").resetPassword({
        password: password.trim(),
        token: code.trim(),
        confirmPassword: confirmPassword.trim(),
      });

      if (response?.success) {
        successMsg(
          "Password reset successfully! Please log in with your new password."
        );
        router.push("/login");
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to reset password.";
        errorMsg(errorMessage);
      }
    } catch (error: any) {
      const errorMessage =
        error?.message || "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSubmit();
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="heading-lg-semibold-24 lg:text-[24px]">
        Reset Password
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Verification Code
        </div>
        <input
          className="body-sm-regular-12 placeholder:text-white-300 border-white-100 mb-2 w-full rounded-[6px] border px-2 py-3 outline-none"
          placeholder="Enter 8-digit verification code"
          value={code}
          onChange={(e) => handleInputChange("code", e.target.value)}
          onKeyDown={handleKeyDown}
          type="text"
          maxLength={8}
          autoComplete="off"
        />
        {errors?.code && (
          <div className="body-sm-regular-12 text-red-500">{errors?.code}</div>
        )}
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">
          New Password
        </div>
        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
          <input
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            placeholder="Enter new password"
            value={password}
            onChange={(e) => handleInputChange("password", e.target.value)}
            onKeyDown={handleKeyDown}
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
          />
          <div
            onClick={() => setShowPassword(!showPassword)}
            className="cursor-pointer"
          >
            {showPassword ? <EyeIcon /> : <EyeCloseIcon />}
          </div>
        </div>
        {errors?.password && (
          <div className="body-sm-regular-12 text-red-500">
            {errors?.password}
          </div>
        )}
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Confirm Password
        </div>
        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
          <input
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            placeholder="Confirm new password"
            value={confirmPassword}
            onChange={(e) =>
              handleInputChange("confirmPassword", e.target.value)
            }
            onKeyDown={handleKeyDown}
            type={showConfirmPassword ? "text" : "password"}
            autoComplete="new-password"
          />
          <div
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="cursor-pointer"
          >
            {showConfirmPassword ? <EyeIcon /> : <EyeCloseIcon />}
          </div>
        </div>
        {errors?.confirmPassword && (
          <div className="body-sm-regular-12 text-red-500">
            {errors?.confirmPassword}
          </div>
        )}
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={handleSubmit}
        isLoading={isLoading}
        disabled={!code.trim() || !password.trim() || !confirmPassword.trim()}
      >
        Reset Password
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/login">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Back to Login
          </div>
        </Link>
      </div>
    </div>
  );
}
