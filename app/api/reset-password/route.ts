import config from "@/config";
import { generateHMAC } from "@/utils/auth";
import axios, { AxiosError } from "axios";
import { NextResponse } from "next/server";

const AUTH_URL = config.apiUrl;
const secretKey = process.env.APP_SECRET_KEY || "";

export async function POST(request: Request) {
  try {
    const { confirmPassword, password, token } = await request.json();
    const timestamp = Date.now();

    const signature = generateHMAC(
      {
        confirmPassword,
        password,
        token,
        timestamp,
      },
      secretKey
    );

    console.log(
      `Calling reset password API with token: ${token} and url ${AUTH_URL}/v1/password/reset`
    );

    await axios.post(
      `${AUTH_URL}/v1/password/reset`,
      { confirmPassword, password, token },
      { headers: { "X-TIMESTAMP": timestamp, "X-SIGNATURE": signature } }
    );

    return NextResponse.json({ success: true });
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      const status = axiosError.response?.status || 500;
      const message =
        (axiosError.response?.data as any)?.message ||
        axiosError.message ||
        "Unexpected error";

      return NextResponse.json({ success: false, error: message }, { status });
    }

    return NextResponse.json(
      { success: false, error: error.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
}
