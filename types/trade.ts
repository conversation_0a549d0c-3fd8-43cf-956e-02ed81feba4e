import { EOrderSideParam } from "@/components/OrderHistory";

export type TUserTradeRequest = {
  symbol: string;
  side: EOrderSideParam;
  orderId?: string;
  startDate?: number;
  endDate?: number;
  fromId?: number;
  limit?: number;
  recvWindow?: number;
};
export type TUserTrade = {
  commission?: string;
  commissionAsset?: string;
  id: number;
  isBestMatch?: boolean;
  isBuyer: boolean;
  isMaker: boolean;
  orderId: number;
  price: string;
  qty: string;
  quoteQty: string;
  symbol: string;
  time: number;
  fee: string;
  baseSymbol?: string;
  quoteSymbol?: string;
};

export type TAggTrade = {
  aggTradeId: number;
  createdAt: number;
  firstTradeId: number;
  isBuyerMaker: boolean;
  lastTradeId: number;
  price: string;
  quantity: string;
  symbol: string;
  timestamp: number;
  updatedAt: number;
  isBuyer: boolean; // Indicates if the buyer is the maker
  isMaker: boolean; // Indicates if the trade is from a maker order
};

export enum ETradeRole {
  MAKER = "Maker",
  TAKER = "Taker",
}

export const OPTIONS_BASE = [
  {
    label: "All",
    value: "",
  },
  {
    label: "1000CAT",
    value: "1000CAT",
  },
  {
    label: "1000CHEMS",
    value: "1000CHEMS",
  },
];

export type TTradeRequest = {
  symbol: string;
  limit?: number;
  fromId?: number;
  startTime?: number;
  endTime?: number;
};

export type TTradeUpdated = {
  s: string; // Symbol (e.g., BTCUSDT)
  i: number; // Trade ID or custom identifier
  o: number; // Order ID (likely a large integer)
  p: string; // Price
  q: string; // Quantity
  Q: string; // Quote quantity
  c: string; // Cost or fee per unit
  C: string; // Quote asset (e.g., USDT)
  f: string; // Fee amount
  T: number; // Timestamp
  b: boolean; // IS buyer?
  m: boolean; // IS maker?
  M: boolean; // Is the trade from a maker order?
};

export type TTradeMarketUpdated = {
  e: "aggTrade"; // Event type
  E: number; // Event time (timestamp)
  s: string; // Symbol (e.g., BTCUSDT)
  a: number; // Aggregate trade ID
  p: string; // Price
  q: string; // Quantity
  f: number; // First trade ID
  l: number; // Last trade ID
  T: number; // Trade time (timestamp)
  m: boolean; // Is the buyer the market maker?
  M: boolean; // Ignore (always true)
};

export const TRADE_TIMEFRAME = {
  "1d": "1d",
  "1w": "1w",
  "1m": "1m", // month
  "3m": "3m", // month
};
