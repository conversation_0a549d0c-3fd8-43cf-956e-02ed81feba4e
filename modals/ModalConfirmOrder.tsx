import { BaseModal } from "@/modals/BaseModal";
import React, { useState } from "react";
import { AppButton } from "@/components";
import { CloseIcon24, CheckboxCheckedIcon, CheckboxIcon } from "@/assets/icons";
import { EOrderType } from "@/components/OrderForm";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";
import { EOrderStopCondition } from "@/types/order";
import AppNumber from "@/components/AppNumber";
import { useMediaQuery } from "react-responsive";
import { setOrderConfirmationPreference } from "@/utils/orderConfirmation";
import BigNumber from "bignumber.js";
import { getOrderTypeDisplay } from "@/utils/helper";

export type TConfirmOrderData = {
  symbol: string;
  orderType: EOrderType;
  side: EOrderSide;
  price?: string;
  amount: string;
  total: string;
  stopPrice?: string;
  stopCondition?: EOrderStopCondition;
  baseAsset: string;
  quoteAsset: string;
  currentPrice?: string;
  pricePrecision?: number;
  quantityPrecision?: number;
};

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  orderData: TConfirmOrderData;
  isLoading?: boolean;
}

export const ModalConfirmOrder = ({
  isOpen,
  onClose,
  onConfirm,
  orderData,
  isLoading = false,
}: Props) => {
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const {
    symbol,
    orderType,
    side,
    price,
    amount,
    total,
    stopPrice,
    stopCondition,
    baseAsset,
    quoteAsset,
    currentPrice,
    pricePrecision = 2,
    quantityPrecision = 6,
  } = orderData;

  const getSideDisplay = () => {
    return side === EOrderSide.BUY ? "Buy" : "Sell";
  };

  const getPriceDisplay = () => {
    if (
      orderType === EOrderType.MARKET ||
      orderType === EOrderType.STOP_MARKET
    ) {
      return "Market";
    }
    return price;
  };

  const getStopConditionText = () => {
    if (!stopPrice || !currentPrice || !stopCondition) return "";

    const action = side === EOrderSide.BUY ? "buy" : "sell";
    const conditionText =
      stopCondition === EOrderStopCondition.GREATER_THAN
        ? "rises to or above"
        : "falls to or below";

    return `If the last price ${conditionText} ${BigNumber(stopPrice)
      .toFixed(pricePrecision)
      .toString()} ${quoteAsset?.toUpperCase()}, an order to ${action} ${BigNumber(
      amount
    )
      .toFixed(quantityPrecision)
      .toString()} ${baseAsset?.toUpperCase()} will be placed.`;
  };

  const handleConfirm = async () => {
    try {
      if (dontShowAgain) {
        setOrderConfirmationPreference(true);
      }
      onConfirm();
    } catch (error) {
      console.error("Error confirming order:", error);
    } finally {
      onClose();
    }
  };

  return (
    <BaseModal
      className="w-screen max-w-[400px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className="flex items-center justify-between py-1">
        <div className="heading-sm-medium-16">{symbol?.toUpperCase()}</div>
        <CloseIcon24 className="cursor-pointer" onClick={onClose} />
      </div>

      <div
        className="mt-3 space-y-2"
        style={{
          color:
            side === EOrderSide.BUY
              ? "var(--color-green-500)"
              : "var(--color-red-400)",
        }}
      >
        {getOrderTypeDisplay(orderType)} / {getSideDisplay()}
      </div>

      <div className="mt-3 space-y-2">
        {/* Stop Price for stop orders */}
        {(orderType === EOrderType.STOP_LIMIT ||
          orderType === EOrderType.STOP_MARKET) &&
          stopPrice && (
            <div className="body-md-regular-14 grid grid-cols-2 py-1">
              <div className="text-white-500">Stop Price</div>
              <div className="flex items-center justify-end gap-1 text-right">
                <AppNumber
                  value={stopPrice}
                  decimals={pricePrecision}
                  isFormatLargeNumber={false}
                />{" "}
                {quoteAsset?.toUpperCase()}
              </div>
            </div>
          )}

        {/* Price */}
        <div className="body-md-regular-14 grid grid-cols-2 py-1">
          <div className="text-white-500">Price</div>
          <div className="text-right">
            {getPriceDisplay() === "Market" ? (
              "Market price"
            ) : (
              <div className="flex items-center justify-end gap-1">
                <AppNumber
                  value={getPriceDisplay() || "0"}
                  decimals={pricePrecision}
                  isFormatLargeNumber={false}
                />{" "}
                {quoteAsset?.toUpperCase()}
              </div>
            )}
          </div>
        </div>

        {/* Amount */}
        {amount && (
          <div className="body-md-regular-14 grid grid-cols-2 py-1">
            <div className="text-white-500">Amount</div>
            <div className="flex items-center justify-end gap-1">
              <AppNumber value={amount} decimals={quantityPrecision} />{" "}
              {baseAsset?.toUpperCase()}
            </div>
          </div>
        )}

        {/* Total */}
        {total && (
          <div className="body-md-regular-14 grid grid-cols-2 py-2">
            <div className="text-white-500">Total</div>
            <div className="flex items-center justify-end gap-1">
              <AppNumber
                value={total}
                decimals={pricePrecision}
                isFormatLargeNumber={false}
              />{" "}
              {quoteAsset?.toUpperCase()}
            </div>
          </div>
        )}

        {/* Stop order note */}
        {(orderType === EOrderType.STOP_LIMIT ||
          orderType === EOrderType.STOP_MARKET) && (
          <div className="body-sm-regular-12 text-white-500 bg-white-50 mt-4 rounded-[6px] p-3">
            {getStopConditionText()}
          </div>
        )}

        {/* Don't show again checkbox */}
        <div className="border-white-200 mt-3 flex items-center gap-3 border-t pt-3">
          <div
            className="cursor-pointer"
            onClick={() => setDontShowAgain(!dontShowAgain)}
          >
            {dontShowAgain ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
          </div>
          <div className="body-sm-regular-12 text-white-500">
            Don&apos;t display double confirmation for order again
          </div>
        </div>
      </div>

      <div className="mt-6 grid grid-cols-2 gap-3">
        <AppButton size="large" onClick={onClose} variant="secondary">
          Cancel
        </AppButton>
        <AppButton
          size="large"
          variant={side === EOrderSide.BUY ? "buy" : "sell"}
          onClick={handleConfirm}
          disabled={isLoading}
          isLoading={isLoading}
        >
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};
