import config from "@/config";
import { DEFAULT_REDIRECT_URI } from "@/constants";
import { AuthData, ELoginProvider } from "@/types/auth";
import axios from "axios";
import crypto from "crypto";
import { signIn } from "next-auth/react";

export const setAuthorizationToRequest = (accessToken: string | null) => {
  if (!accessToken) {
    delete axios.defaults.headers.common.Authorization;
    return;
  }
  axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
};

export function generateHMAC(data: AuthData, secretKey: string): string {
  // Sort keys to ensure consistent order when generating HMAC
  const keys = Object.keys(data).sort();

  // Create a string by joining all values with a delimiter
  const dataString = keys
    .map((key) => `${key}:${(data as Record<string, any>)[key]}`)
    .join("|");

  const hmac = crypto.createHmac("sha256", secretKey);
  hmac.update(dataString);
  return hmac.digest("hex");
}

export const getRedirectPath = (redirectUrl?: string | null): string => {
  if (!redirectUrl) {
    return `/${DEFAULT_REDIRECT_URI}`;
  }

  // If redirectUrl is a full URL, extract only the pathname
  if (redirectUrl.startsWith("http://") || redirectUrl.startsWith("https://")) {
    try {
      const url = new URL(redirectUrl);
      return url.pathname;
    } catch {
      // If URL parsing fails, treat as path
      return redirectUrl.startsWith("/") ? redirectUrl : `/${redirectUrl}`;
    }
  }

  // If it's a relative path, ensure it starts with /
  return redirectUrl.startsWith("/") ? redirectUrl : `/${redirectUrl}`;
};

export const handleSocialLogin = async (
  provider: ELoginProvider,
  redirectUrl?: string | null
) => {
  try {
    const callbackUrl = getRedirectPath(redirectUrl);

    await signIn(provider, {
      callbackUrl,
    });
  } catch (error) {
    console.log(`handleSocialLogin error`, error);
  }
};

export const handleCallbackProvider = async (
  accessToken: string,
  provider: ELoginProvider
) => {
  const AUTH_URL = config.apiUrl;
  const SECRET_KEY = process.env.APP_SECRET_KEY || "";

  try {
    const timestamp = new Date().valueOf();
    const signature = generateHMAC(
      {
        accessToken,
        timestamp,
      },
      SECRET_KEY
    );

    const { data: user } = await axios.post(
      `${AUTH_URL}/auth/${provider}/callback`,
      {
        accessToken,
      },
      {
        headers: {
          "X-TIMESTAMP": timestamp,
          "X-SIGNATURE": signature,
        },
      }
    );

    return user;
  } catch (error) {
    console.log(`handleCallbackProvider error`, error);
    throw error;
  }
};
