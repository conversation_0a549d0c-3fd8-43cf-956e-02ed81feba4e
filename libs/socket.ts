"use client";

import { io } from "socket.io-client";
import { AppBroadcast, BROADCAST_EVENTS } from "./broadcast";
import config from "@/config";
import { formatOrderUpdated, formatTradeUpdated } from "@/utils/format";

declare global {
  interface Window {
    sockets: {
      [ESocketType: string]: ReturnType<typeof io> | null;
    };
  }
}

if (typeof window !== "undefined") {
  window.sockets = {};
}

export const SOCKET_EVENTS = {
  TRADES: "UpdateAggTrade",
  ORDERBOOK_UPDATED: "UpdateOrderbook",
  NEW_TRANSACTION: "NewTransaction",
  UPDATED_TRANSACTION: "UpdatedTransaction",
  ACCOUNT_UPDATED: "AccountUpdate",
  BALANCE_UPDATED: "UserBalanceUpdate",
  ORDER_UPDATED: "UpdatedOrder",
  NEW_TRADE: "NewTrade",
  UPDATE_CANDLE: "UpdateCandle",
  TICKER_UPDATED: "UpdateTicker",
  UPDATE_TICKER_ARR: "UpdateTickerArr",
  KYC_STATUS_UPDATED: "KycStatusUpdate",
};

export const SOCKET_NETWORK = {
  VDAX: "vdax",
};

export const SOCKETS_ROOMS = {
  ORDERBOOK_UPDATED: "ORDERBOOK_UPDATED",
};

export const socketInstance = (key: string): ReturnType<typeof io> | null =>
  window.sockets[key];

const createInstanceSocket = (socketUrl: string, accessToken?: string) => {
  return io(socketUrl, {
    transports: ["websocket", "polling"],
    reconnectionDelayMax: 5000,
    autoConnect: true,
    reconnection: true,
    timeout: 20000, // Connection timeout (20 seconds)
    ...(accessToken && { query: { authorization: accessToken } }),
    auth: { offset: undefined },
  });
};

export const closeInstanceSocket = (
  socketInstance: ReturnType<typeof io> | null
) => {
  if (socketInstance) {
    socketInstance.removeAllListeners();
    socketInstance.close();
  }
};

export const getSocketInstance = (network: string) => {
  if (!network) return null;
  const socketKey = `${network}`?.toUpperCase();
  return socketInstance(socketKey);
};

export const createSocketInstance = ({
  network,
  accessToken,
}: {
  network: string;
  accessToken?: string;
}) => {
  const socketKey = `${network}`?.toUpperCase();
  const socketUrl = config?.wsUrl;

  if (!socketUrl) {
    console.error("Socket URL is not defined");
    return;
  }

  window.sockets[socketKey] = createInstanceSocket(socketUrl, accessToken);

  window.sockets[socketKey]?.on("connect", () => {
    console.log(
      `Websocket public connection ${network} is connected with server ${socketUrl}`
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_CONNECTED, {});

    const instance = getSocketInstance(network);

    // Remove all existing listeners before adding new ones to prevent duplicates
    Object.values(SOCKET_EVENTS).forEach((event) => {
      console.log("[Socket] remove all listeners", event);
      instance?.removeAllListeners(event);
    });

    instance?.on(SOCKET_EVENTS.TRADES, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.TRADE_UPDATE, data);
    });

    instance?.on(SOCKET_EVENTS.ORDERBOOK_UPDATED, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.ORDERBOOK_UPDATED, data);
    });

    instance?.on(SOCKET_EVENTS.BALANCE_UPDATED, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.BALANCE_UPDATED, data);
    });

    instance?.on(SOCKET_EVENTS.KYC_STATUS_UPDATED, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.KYC_STATUS_UPDATED, data);
    });

    instance?.on(SOCKET_EVENTS.ORDER_UPDATED, (data: any) => {
      const formattedData = formatOrderUpdated(JSON.parse(data));
      AppBroadcast.dispatch(BROADCAST_EVENTS.ORDER_UPDATED, formattedData);
    });

    instance?.on(SOCKET_EVENTS.NEW_TRADE, (data: any) => {
      const formattedData = formatTradeUpdated(JSON.parse(data));
      AppBroadcast.dispatch(BROADCAST_EVENTS.USER_TRADE_UPDATE, formattedData);
    });

    instance?.on(SOCKET_EVENTS.UPDATE_CANDLE, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.UPDATE_CANDLE, data);
    });

    instance?.on(SOCKET_EVENTS.NEW_TRANSACTION, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.NEW_TRANSACTION, data);
    });

    instance?.on(SOCKET_EVENTS.UPDATED_TRANSACTION, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.UPDATED_TRANSACTION, data);
    });

    instance?.on(SOCKET_EVENTS.TICKER_UPDATED, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.TICKER_UPDATED, data);
    });

    instance?.on(SOCKET_EVENTS.UPDATE_TICKER_ARR, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.ARR_TICKERS_UPDATED, data);
    });

    window.sockets[socketKey]?.on("disconnect", (reason, details) => {
      console.log(
        `Websocket public connection ${network} is disconnected`,
        reason,
        details
      );
      AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_DISCONNECTED, {});
    });
  });

  window.sockets[socketKey]?.on("disconnect", (reason, details) => {
    console.log(
      `Websocket public connection ${network} is disconnected`,
      reason,
      details
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_DISCONNECTED, {});
  });
};

export const closeSocketInstance = (network: string) => {
  if (!network) return;
  const socketKey = `${network}`?.toUpperCase();

  closeInstanceSocket(socketInstance(socketKey));
  window.sockets[socketKey] = null;
};

export const subscribeSocketChannel = ({
  params,
  authorization,
}: {
  params: string[];
  authorization?: string;
}) => {
  getSocketInstance(SOCKET_NETWORK.VDAX)?.emit("SUBSCRIBE", {
    params,
    method: "SUBSCRIBE",
    authorization,
  });
};

export const unsubscribeSocketChannel = ({ params }: { params: string[] }) => {
  getSocketInstance(SOCKET_NETWORK.VDAX)?.emit("UNSUBSCRIBE", {
    params: params,
    method: "UNSUBSCRIBE",
  });
};

export const getOrderbookRoomName = (symbol: string) => {
  return `${symbol?.toUpperCase()}@orderbook`;
};

export const getPrivateUserRoomName = (userId: string) => {
  return `private:user:${userId}`;
};

export const getAggTradeRoomName = (symbol: string) => {
  return `${symbol?.toUpperCase()}@aggTrade`;
};

export const getCandleRoomName = (symbol: string, resolution: string) => {
  return `${symbol?.toUpperCase()}@candle_${resolution}`;
};

export const getTickerRoom = (symbol: string) => {
  return `${symbol?.toUpperCase()}@ticker`;
};

export const getArrTickerRoom = () => {
  return "ticker@arr";
};
