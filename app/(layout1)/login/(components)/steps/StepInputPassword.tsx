"use client";

import { GoogleIcon, EyeIcon, EyeCloseIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { DEFAULT_REDIRECT_URI } from "@/constants";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { ELoginProvider } from "@/types/auth";
import { ErrorStatus } from "@/types/errors";
import { handleSocialLogin, getRedirectPath } from "@/utils/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useContext, useState } from "react";
import { LoginStepContext } from "../contexts/LoginStepContext";
import { ELoginStep } from "../steps";

type StepInputPasswordProps = {
  setStep: (step: ELoginStep) => void;
  mailOrPhone: string;
  password: string;
  setPassword: (password: string) => void;
  setIsNeedVerifyCode: (value: boolean) => void;
};

export const StepInputPassword: React.FC<StepInputPasswordProps> = ({
  setStep,
  mailOrPhone,
  password,
  setPassword,
  setIsNeedVerifyCode,
}) => {
  const [errors, setErrors] = useState({
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isShowPassword, setIsShowPassword] = useState(false);
  const router = useRouter();
  const { redirectUrl } = useContext(LoginStepContext)!;

  const handleNewUserFlow = async () => {
    const newUserResponse = await rf.getRequest("AuthRequest").checkNewUser();

    if (!newUserResponse.success || !newUserResponse.isNewUser) {
      return false;
    }

    await rf.getRequest("AuthRequest").clearNewUserCookie();
    setStep(ELoginStep.WelcomeAboard);
    return true;
  };

  const handleExistingUserFlow = async () => {
    const finalRedirectPath = getRedirectPath(redirectUrl);

    await router.push(finalRedirectPath);
    await router.refresh();
  };

  const handleMFARequired = (errorMessage: string) => {
    if (!errorMessage?.includes("MFA code is required")) {
      return false;
    }

    setIsNeedVerifyCode(true);
    setStep(ELoginStep.VerifyMFA);
    return true;
  };

  const handleLoginError = (response: any) => {
    const errorMessage =
      response?.response?.data?.error || response.error || "Login error";

    if (response.status !== ErrorStatus.UNAUTHORIZED) {
      errorMsg(errorMessage);
      return;
    }

    if (handleMFARequired(errorMessage)) {
      return;
    }

    setErrors({ password: errorMessage });
  };

  const handleLogin = async () => {
    setIsLoading(true);

    try {
      const response = await rf.getRequest("AuthRequest").login({
        email: mailOrPhone,
        password,
      });

      if (!response?.success) {
        handleLoginError(response);
        return;
      }

      const isNewUser = await handleNewUserFlow();

      if (!isNewUser) {
        await handleExistingUserFlow();
      }

      successMsg("Login Successfully!");
    } catch (error: any) {
      errorMsg(error?.message || "Login error");
    } finally {
      setIsLoading(false);
    }
  };

  const onNext = async () => {
    if (!password) {
      setErrors({
        password: "Please enter your password.",
      });
      return;
    }

    setErrors({
      password: "",
    });
    await handleLogin();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      onNext();
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (errors.password) {
      setErrors({
        password: "",
      });
    }
  };

  const handleClickOutside = () => {
    setErrors({
      password: "",
    });
  };

  return (
    <>
      <div className="heading-lg-semibold-24 lg:text-[24px]">
        Enter your password
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">Password</div>

        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
          <input
            key="input-login-password"
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            value={password}
            autoFocus
            onKeyDown={handleKeyDown}
            onBlur={handleClickOutside}
            type={isShowPassword ? "text" : "password"}
            autoComplete="current-password"
            onChange={handlePasswordChange}
          />
          <div
            onClick={() => setIsShowPassword(!isShowPassword)}
            className="cursor-pointer"
          >
            {isShowPassword ? <EyeIcon /> : <EyeCloseIcon />}
          </div>
        </div>

        {errors?.password && (
          <div className="body-sm-regular-12 text-red-500">
            {errors?.password}
          </div>
        )}

        <div className="flex justify-end">
          <Link href="/forgot-password">
            <div className="body-sm-medium-12 cursor-pointer text-green-500 hover:text-green-400">
              Forgot Password?
            </div>
          </Link>
        </div>
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={onNext}
        isLoading={isLoading}
        disabled={!password.trim()}
      >
        Log In
      </AppButton>

      <div className="flex items-center gap-1">
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
        <div className="body-md-regular-14 text-white-800">Or</div>
        <div className="bg-white-100 h-[1px] w-[calc(50%-4px)]"></div>
      </div>

      <AppButton
        variant="outline"
        size="large"
        className="relative"
        onClick={() => handleSocialLogin(ELoginProvider.GOOGLE, redirectUrl)}
      >
        <GoogleIcon className="absolute left-4" /> Continue with Google
      </AppButton>

      <div className="flex justify-center py-2.5">
        <Link href="/register">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Create a VDAX Account
          </div>
        </Link>
      </div>
    </>
  );
};
