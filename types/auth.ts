export type TRegisterParams = {
  email: string;
  password: string;
};

export type TVerifyCodeParams = {
  email: string;
  code: string;
};

export type TResendVerificationCodeParams = {
  email: string;
};

export type TLoginParams = {
  email: string;
  password: string;
  mfaCode?: string;
};

export type TResetPasswordParams = {
  confirmPassword: string;
  password: string;
  token: string;
};

export type AuthData = {
  [key: string]: string | number;
};

export enum ELoginProvider {
  GOOGLE = "google",
}

export type TTwoFAVerifyParams = {
  authenticatorCode?: string;
  emailCode?: string;
  [key: string]: any;
};

export type TInternalGoogleAuthParams = {
  accessToken: string;
};
