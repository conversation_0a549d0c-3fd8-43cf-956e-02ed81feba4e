"use client";

import React, { useState, useEffect } from "react";
import { AppButton } from "@/components";
import { errorMsg, successMsg } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import Link from "next/link";
import { useRouter } from "next/navigation";
import validator from "validator";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState({ email: "" });
  const [countdown, setCountdown] = useState<number>(0);
  const [allowResend, setAllowResend] = useState<boolean>(true);
  const [emailSent, setEmailSent] = useState<boolean>(false);
  const router = useRouter();

  // Countdown timer effect
  useEffect(() => {
    if (countdown === 0) {
      setAllowResend(true);
      return;
    }

    const intervalId = setInterval(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [countdown]);

  // Format countdown time for display
  const formatCountdown = (seconds: number): string => {
    if (seconds <= 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    // Clear error when user starts typing
    if (errors.email) {
      setErrors({ email: "" });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSubmit();
    }
  };

  const validateEmail = () => {
    if (!email.trim()) {
      setErrors({ email: "Please enter your email address." });
      return false;
    }

    if (!validator.isEmail(email)) {
      setErrors({ email: "Please enter a valid email address." });
      return false;
    }

    setErrors({ email: "" });
    return true;
  };

  const handleSubmit = async () => {
    if (!validateEmail()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await rf
        .getRequest("AuthRequest")
        .requestForgotPassword(email);

      if (response?.success) {
        successMsg("Password reset instructions have been sent to your email.");
        setEmailSent(true);
        setCountdown(60);
        setAllowResend(false);
        // Redirect to reset-password page with email parameter
        router.push(`/reset-password?email=${encodeURIComponent(email)}`);
      } else {
        const errorMessage =
          response?.response?.data?.error ||
          response?.error ||
          "Failed to send reset email.";
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again.";
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="heading-lg-semibold-24 lg:text-[24px]">
        Forgot Password
      </div>

      <div className="body-md-regular-14 text-white-700">
        Enter your email address and we&apos;ll send you a verification code to
        reset your password.
      </div>

      <div>
        <div className="body-sm-medium-12 text-white-700 mb-2">Email</div>
        <input
          className="body-sm-regular-12 placeholder:text-white-300 border-white-100 mb-2 w-full rounded-[6px] border px-2 py-3 outline-none"
          placeholder="Please enter your email"
          value={email}
          autoFocus
          onChange={handleEmailChange}
          onKeyDown={handleKeyDown}
          type="email"
          autoComplete="email"
        />
        {errors?.email && (
          <div className="body-sm-regular-12 text-red-500">{errors?.email}</div>
        )}
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={handleSubmit}
        isLoading={isLoading}
        disabled={!email.trim() || (!allowResend && emailSent)}
      >
        {!allowResend && emailSent && countdown > 0
          ? `Resend in ${formatCountdown(countdown)}`
          : emailSent && allowResend
          ? "Resend Reset Instructions"
          : "Send Reset Instructions"}
      </AppButton>

      {emailSent && !allowResend && countdown > 0 && (
        <div className="body-sm-regular-12 text-white-500 mt-2 text-center">
          You can request a new reset email in {formatCountdown(countdown)}
        </div>
      )}

      <div className="flex justify-center py-2.5">
        <Link href="/login">
          <div className="body-md-medium-14 cursor-pointer text-green-500">
            Back to Login
          </div>
        </Link>
      </div>
    </div>
  );
}
