import config from "@/config";
import { generateHMAC } from "@/utils/auth";
import axios, { AxiosError } from "axios";
import { NextResponse } from "next/server";

const AUTH_URL = config.apiUrl;
const secretKey = process.env.APP_SECRET_KEY || "";

export async function POST(request: Request) {
  try {
    const { email } = await request.json();
    const timestamp = Date.now();

    const signature = generateHMAC(
      {
        email,
        timestamp,
      },
      secretKey
    );

    console.log(
      `Calling forgot password API with email: ${email} and url ${AUTH_URL}/v1/password/forgot`
    );

    await axios.post(
      `${AUTH_URL}/v1/password/forgot`,
      { email },
      { headers: { "X-TIMESTAMP": timestamp, "X-SIGNATURE": signature } }
    );

    return NextResponse.json({ success: true });
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      const status = axiosError.response?.status || 500;
      const message =
        (axiosError.response?.data as any)?.message ||
        axiosError.message ||
        "Unexpected error";

      return NextResponse.json({ success: false, error: message }, { status });
    }

    return NextResponse.json(
      { success: false, error: error.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
}
