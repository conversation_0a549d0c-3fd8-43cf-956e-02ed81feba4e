"use client";

import { OrderBookBuySell, OrderBookBuy, OrderBookSell } from "@/assets/icons";
import React, { memo, useEffect, useRef, useState } from "react";
import { ListOrder } from "./components/ListOrder";
import { EOrderbook, OrderbookData } from "@/types/OrderBook";
import OrderbookEventHandler from "@/components/OrderBook/services/OrderbookEventHandler";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import CurrentPrice from "./components/CurrentPrice";
import SelectDecimal from "./components/SelectDecimal";
import BigNumber from "bignumber.js";
import { MarketSentiment } from "./components/MarketSentiment";
import {
  getOrderbookRoomName,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
// import { OrderBookSettings } from "./components/OrderBookSetting";
import { useMediaQuery } from "react-responsive";

export const OrderBook = memo(() => {
  const [orderbookType, setOrderbookType] = useState<EOrderbook>(
    EOrderbook.ALL
  );
  const [decimal, setDecimal] = useState<number>(0.01);
  const [settings, setSettings] = useState<any>({
    isDisplayAvg: true,
    isShowRatio: true,
    isRounding: true,
    bookDeep: "Cumulative",
    animation: false,
  });
  const orderbookRef = useRef<OrderbookData>({
    lastUpdateId: 0,
    bids: [],
    asks: [],
  });
  const askScrollRef = useRef<HTMLDivElement>(null);
  const { pairSetting } = usePairContext();
  const [displayItemNumber, setDisplayItemNumber] = useState(20);
  const [forceRender, setForceRender] = useState(0);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    const onUpdate = (orderbook: OrderbookData) => {
      orderbookRef.current = orderbook;
    };
    const orderbookHandler = new OrderbookEventHandler(
      pairSetting?.symbol || "",
      onUpdate
    );

    const handleOrderbookEvent = (data: TBroadcastEvent) => {
      orderbookHandler.processDepthEvent(JSON.parse(data.detail));
    };

    orderbookHandler.initOrderbook();
    AppBroadcast.on(BROADCAST_EVENTS.ORDERBOOK_UPDATED, handleOrderbookEvent);

    // Cleanup function
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDERBOOK_UPDATED,
        handleOrderbookEvent
      );
    };
  }, [pairSetting?.symbol]);

  useEffect(() => {
    if (!pairSetting?.symbol || !socketConnected) return;

    subscribeSocketChannel({
      params: [getOrderbookRoomName(pairSetting?.symbol)],
    });

    return () => {
      unsubscribeSocketChannel({
        params: [getOrderbookRoomName(pairSetting?.symbol)],
      });
    };
  }, [pairSetting?.symbol, socketConnected]);

  useEffect(() => {
    if (orderbookType == EOrderbook.ALL) {
      setDisplayItemNumber(20);
    } else if ([EOrderbook.BID, EOrderbook.ASK].includes(orderbookType)) {
      setDisplayItemNumber(1000);
    }
  }, [orderbookType]);

  useEffect(() => {
    if (orderbookType === EOrderbook.ASK && askScrollRef.current) {
      setTimeout(() => {
        if (askScrollRef.current) {
          askScrollRef.current.scrollTop = askScrollRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [orderbookType, forceRender]);

  useEffect(() => {
    const intervalRerender = setInterval(() => {
      setForceRender((state) => state + 1);
    }, 500);

    return () => {
      clearInterval(intervalRerender);
    };
  }, []);

  const calculateMarketSentiment = (orderbook: OrderbookData | undefined) => {
    if (!orderbook?.asks || !orderbook?.bids) {
      return { bidPercent: 0, askPercent: 0 };
    }

    // Calculate total volume
    const totalBidVolume = orderbook.bids.reduce(
      (sum, [_, amount]) => BigNumber(sum).plus(amount).toNumber(),
      0
    );
    const totalAskVolume = orderbook.asks.reduce(
      (sum, [_, amount]) => BigNumber(sum).plus(amount).toNumber(),
      0
    );
    const totalVolume = totalBidVolume + totalAskVolume;

    if (!totalVolume) {
      return {
        bidPercent: 0,
        askPercent: 0,
      };
    }

    // Calculate percentages
    const bidPercent =
      Math.round((totalBidVolume / totalVolume) * 100 * 100) / 100;
    const askPercent = Math.round((100 - bidPercent) * 100) / 100;

    return { bidPercent, askPercent };
  };

  const renderContent = () => {
    const { asks = [], bids = [] } = orderbookRef.current;

    const { bidPercent, askPercent } =
      orderbookType === EOrderbook.ALL
        ? calculateMarketSentiment(orderbookRef.current)
        : { bidPercent: 0, askPercent: 0 };

    switch (orderbookType) {
      case EOrderbook.ALL:
        return (
          <div>
            {isMobile ? (
              <div className="grid grid-cols-2">
                <div>
                  <div className="grid grid-cols-2">
                    <div className="body-sm-regular-12 text-white-500 p-2">
                      Amount
                    </div>
                    <div className="body-sm-regular-12 text-white-500 p-2">
                      Price
                    </div>
                  </div>
                  <div className="hide-scroll h-[400px] overflow-hidden">
                    <ListOrder
                      isHideTotal
                      orderbook={bids}
                      decimal={decimal}
                      orderbookType={EOrderbook.BID}
                      displayItemNumber={displayItemNumber}
                    />
                  </div>
                </div>
                <div>
                  <div className="grid grid-cols-2">
                    <div className="body-sm-regular-12 text-white-500 p-2">
                      Price
                    </div>
                    <div className="body-sm-regular-12 text-white-500 p-2 text-right">
                      Amount
                    </div>
                  </div>
                  <div className="hide-scroll h-[400px] overflow-hidden">
                    <ListOrder
                      isHideTotal
                      orderbook={asks}
                      decimal={decimal}
                      orderbookType={EOrderbook.ASK}
                      displayItemNumber={displayItemNumber}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="hidden lg:block">
                <div className="hide-scroll flex h-[288px] flex-col justify-end overflow-hidden">
                  <ListOrder
                    orderbook={asks}
                    decimal={decimal}
                    orderbookType={EOrderbook.ASK}
                    displayItemNumber={displayItemNumber}
                  />
                </div>
                <CurrentPrice />
                <div className="hide-scroll h-[288px] overflow-hidden">
                  <ListOrder
                    orderbook={bids}
                    decimal={decimal}
                    orderbookType={EOrderbook.BID}
                    displayItemNumber={displayItemNumber}
                  />
                </div>
                <MarketSentiment
                  bidPercent={bidPercent}
                  askPercent={askPercent}
                />
              </div>
            )}
          </div>
        );

      case EOrderbook.BID:
        return (
          <div>
            <div className="hidden lg:block">
              <CurrentPrice />
            </div>

            <div className="customer-scroll h-[400px] overflow-y-scroll lg:h-[602px]">
              <ListOrder
                orderbook={bids}
                decimal={decimal}
                orderbookType={EOrderbook.BID}
                displayItemNumber={displayItemNumber}
              />
            </div>
          </div>
        );

      case EOrderbook.ASK:
        return (
          <div>
            <div
              ref={askScrollRef}
              className="customer-scroll h-[400px] overflow-y-scroll lg:h-[602px]"
            >
              <ListOrder
                orderbook={asks}
                decimal={decimal}
                orderbookType={EOrderbook.ASK}
                displayItemNumber={displayItemNumber}
              />
            </div>
            <div className="hidden lg:block">
              <CurrentPrice />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full px-2 lg:px-0">
      <div className="border-white-100 hidden items-center justify-between border-b p-2 lg:flex">
        <div className="body-md-medium-14">OrderBook</div>
        {/* <OrderBookSettings setSettings={setSettings} settings={settings} /> */}
      </div>
      <div className="flex w-full items-center justify-between p-2">
        <div className="flex items-center gap-3">
          <OrderBookBuySell
            onClick={() => setOrderbookType(EOrderbook.ALL)}
            className={`${
              orderbookType === EOrderbook.ALL ? "" : "opacity-50"
            } cursor-pointer`}
          />
          <OrderBookBuy
            onClick={() => setOrderbookType(EOrderbook.BID)}
            className={`${
              orderbookType === EOrderbook.BID ? "" : "opacity-50"
            } cursor-pointer`}
          />
          <OrderBookSell
            onClick={() => setOrderbookType(EOrderbook.ASK)}
            className={`${
              orderbookType === EOrderbook.ASK ? "" : "opacity-50"
            } cursor-pointer`}
          />
        </div>

        <SelectDecimal decimals={decimal} setDecimals={setDecimal} />
      </div>

      <div
        className={`grid-cols-3 ${
          orderbookType === EOrderbook.ALL ? "hidden lg:grid" : "grid"
        }`}
      >
        <div className="body-sm-regular-12 text-white-500 p-2">
          Price {pairSetting?.quoteAsset?.toUpperCase()}
        </div>
        <div className="body-sm-regular-12 text-white-500 p-2 text-right">
          Amount {pairSetting?.baseAsset?.toUpperCase()}
        </div>
        <div className="body-sm-regular-12 text-white-500 p-2 text-right">
          Total
        </div>
      </div>

      <div>{renderContent()}</div>
    </div>
  );
});

OrderBook.displayName = "OrderBook";
