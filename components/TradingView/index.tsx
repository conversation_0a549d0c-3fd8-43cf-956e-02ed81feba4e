"use client";
import {
  Bar,
  ChartingLibraryWidgetOptions,
  GetMarksCallback,
  HistoryCallback,
  IBasicDataFeed,
  LibrarySymbolInfo,
  Mark,
  PeriodParams,
  ResolutionString,
  ResolveCallback,
  SubscribeBarsCallback,
  ThemeName,
  widget,
} from "@/libs/charting_library";
import rf from "@/services/RequestFactory";
import { TCandle, TUpdatedCandleWsData } from "@/types/pair";
import * as React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { SYMBOL_TYPE } from "./utils/consts";
import {
  DATAFEED_CONFIGURATION,
  DISABLED_FEATURES,
  ENABLED_FEATURES,
  getClientTimezone,
} from "./utils/setting";
import { useMediaQuery } from "react-responsive";
import {
  convertResolutionString2Unit,
  getQuoteVolume,
  isInvalidCandle,
} from "./utils/helper";
import { priceFormatterFactory } from "./utils/format";
import {
  getCandleRoomName,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { BROADCAST_EVENTS, TBroadcastEvent } from "@/libs/broadcast";
import { AppBroadcast } from "@/libs/broadcast";

const DEFAULT_LIBRARY_PATH = "/static/charting_library/";
const DEFAULT_CLIENT_ID = "tradingview.com";
const DEFAULT_USER_ID = "public_user_id";
const CHART_PROPERTIES_KEY = "tradingview.chartproperties";

export interface ChartProps {
  symbol: ChartingLibraryWidgetOptions["symbol"];
  interval?: ChartingLibraryWidgetOptions["interval"];
  fullscreen?: ChartingLibraryWidgetOptions["fullscreen"];
  autosize?: ChartingLibraryWidgetOptions["autosize"];
  // container: ChartingLibraryWidgetOptions["container"];
}

const getInitialChartInterval = () => {
  if (typeof window === "undefined") {
    return DEFAULT_RESOLUTION_STRING;
  }

  const resolutionParam = new URLSearchParams(window.location.search).get(
    "resolution"
  );
  const lastUsedResolution = localStorage.getItem(
    "tradingview.chart.lastUsedTimeBasedResolution"
  );

  if (
    resolutionParam &&
    DATAFEED_CONFIGURATION.supported_resolutions.includes(resolutionParam)
  ) {
    return resolutionParam as ResolutionString;
  }

  return (lastUsedResolution || DEFAULT_RESOLUTION_STRING) as ResolutionString;
};

const DEFAULT_RESOLUTION_STRING = "1S" as ResolutionString;

const TradingView = React.memo((chartProps: ChartProps) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartInterval, setChartInterval] = useState<ResolutionString>(
    getInitialChartInterval()
  );

  const lastCandleRef = useRef<Bar>({} as Bar);
  const tradingViewChart = useRef<any>({});

  const onTickRef = useRef<SubscribeBarsCallback>({} as SubscribeBarsCallback);
  const resolutionStringRef = useRef<ResolutionString>(
    DEFAULT_RESOLUTION_STRING
  );

  const chartContainerRef = useRef<HTMLDivElement>(
    null
  ) as React.RefObject<HTMLInputElement>;

  // Function to override localStorage chart properties
  const overrideChartProperties = useCallback(() => {
    if (typeof window === "undefined") return;

    try {
      const currentProperties = localStorage.getItem(CHART_PROPERTIES_KEY);
      if (!currentProperties) return;

      const chartProperties = JSON.parse(currentProperties);

      // Check if paneProperties exists and has background properties
      if (chartProperties.paneProperties) {
        const hasBackgroundGradientStart =
          chartProperties.paneProperties.backgroundGradientStartColor;
        const hasBackgroundGradientEnd =
          chartProperties.paneProperties.backgroundGradientEndColor;
        const hasBackground = chartProperties.paneProperties.background;

        // Override background properties if they exist
        if (
          hasBackgroundGradientStart ||
          hasBackgroundGradientEnd ||
          hasBackground
        ) {
          chartProperties.paneProperties.backgroundGradientStartColor =
            "#151618";
          chartProperties.paneProperties.backgroundGradientEndColor = "#151618";
          chartProperties.paneProperties.background = "#151618";

          // Save updated properties to localStorage
          localStorage.setItem(
            CHART_PROPERTIES_KEY,
            JSON.stringify(chartProperties)
          );
          console.log("Background properties overridden in localStorage");
        }
      }
    } catch (error) {
      console.error("Error overriding chart properties:", error);
    }
  }, []);

  // Override chart properties immediately when component initializes
  overrideChartProperties();

  useEffect(() => {
    setIsChartReady(false);
  }, []);

  const getActiveChart = () => {
    try {
      return tradingViewChart.current?.activeChart();
    } catch (e) {
      console.error("getActiveChart", e);
      return null;
    }
  };

  useEffect(() => {
    if (!isChartReady) {
      return;
    }

    if (tradingViewChart?.current) {
      getActiveChart()?.refreshMarks();
    }
  }, [isChartReady]);

  useEffect(() => {
    if (!chartProps?.symbol || !chartInterval) return;
    subscribeSocketChannel({
      params: [
        getCandleRoomName(
          chartProps?.symbol,
          convertResolutionString2Unit(chartInterval)
        ),
      ],
    });
    return () => {
      if (chartProps?.symbol && chartInterval) {
        unsubscribeSocketChannel({
          params: [
            getCandleRoomName(
              chartProps?.symbol,
              convertResolutionString2Unit(chartInterval)
            ),
          ],
        });
      }
    };
  }, [chartProps?.symbol, chartInterval]);

  const getBars = useCallback(
    async (
      symbolInfo: LibrarySymbolInfo,
      resolution: ResolutionString,
      periodParams: PeriodParams,
      onResult: HistoryCallback
    ) => {
      if (!chartProps?.symbol) return;

      if (Number(periodParams.from) <= 0 || Number(periodParams.to) <= 0) {
        return onResult([], {
          noData: true,
        });
      }

      const from = periodParams.from;
      const to = periodParams.to;

      const resolutionUnit = convertResolutionString2Unit(resolution);
      const candleQueryParams = {
        limit: Math.ceil(Math.min(periodParams.countBack, 300) * 1.2), // buffer 20% for more data
        endTime: to * 1000,
        symbol: chartProps?.symbol?.toUpperCase(),
        interval: resolutionUnit,
      } as any;

      // if (!periodParams.firstDataRequest) {
      //   candleQueryParams.startTime = from * 1000;
      // }

      try {
        const res = await rf
          .getRequest("CandleRequest")
          .getCandles(candleQueryParams);

        const listCandle = res?.data || [];

        const validListCandle = listCandle.filter((candle: TCandle) => {
          return !isInvalidCandle(candle);
        });

        if (!validListCandle.length) {
          return onResult([], {
            noData: true,
          });
        }

        const bars: any = validListCandle
          .sort((a: TCandle, b: TCandle) => +a[0] - +b[0]) // timestamp
          .map((candle: TCandle) => {
            return {
              time: +candle[0], // timestamp
              open: +candle[1], // open price
              high: +candle[2], // high price
              low: +candle[3], // low price
              close: +candle[4], // close price
              volume: getQuoteVolume(candle),
            };
          });

        const lastCandle = bars[bars.length - 1];

        if (periodParams.firstDataRequest) {
          lastCandleRef.current = lastCandle;
        }

        onResult(bars, { noData: false });
      } catch (error: any) {
        console.log(`GetBars onError:`, error);
        onResult([], { noData: true });
        // onError('Something went wrong!');
      }
    },
    [chartProps.symbol]
  );

  const subscribeBars = (
    symbolInfo: LibrarySymbolInfo,
    resolution: ResolutionString,
    onTick: SubscribeBarsCallback,
    listenerGuid: string,
    onResetCacheNeededCallback: () => void
  ) => {
    console.log("subscribeBars", {
      symbolInfo,
      resolution,
      onTick,
      listenerGuid,
      onResetCacheNeededCallback,
    });
    resolutionStringRef.current = resolution;
    onTickRef.current = onTick;
  };

  useEffect(() => {
    if (!chartProps?.symbol) {
      console.warn("[TradingView] handleUpdateCandle: symbol not found");
      return;
    }

    const handleUpdateCandle = (event: TBroadcastEvent) => {
      const data: TUpdatedCandleWsData = JSON.parse(event.detail);
      if (chartProps?.symbol?.toUpperCase() !== data?.s?.toUpperCase()) {
        console.warn("[TradingView] handleUpdateCandle: symbol not match", {
          chartPropsSymbol: chartProps?.symbol?.toUpperCase(),
          dataSymbol: data?.s?.toUpperCase(),
        });
        return;
      }
      // if (convertResolutionString2Unit(chartInterval) !== data?.k?.i && chartInterval !== data?.k?.i) return;
      try {
        const currentResolution = convertResolutionString2Unit(
          resolutionStringRef.current
        );
        const receivedResolution = convertResolutionString2Unit(
          data.k.i as ResolutionString
        );

        if (currentResolution !== receivedResolution) {
          console.warn(
            "[TradingView] handleUpdateCandle: resolution not match",
            {
              currentResolution,
              receivedResolution,
            }
          );
          return;
        }

        const newCandle = {
          time: data.k.t,
          open: +data.k.o,
          high: +data.k.h,
          low: +data.k.l,
          close: +data.k.c,
          volume: +data.k.q,
        };

        lastCandleRef.current = newCandle;
        onTickRef.current(newCandle);
      } catch (error: any) {
        console.error(
          "handleUpdateCandle: UpdateCandle throw error",
          error?.message
        );
      }
    };

    AppBroadcast.on(BROADCAST_EVENTS.UPDATE_CANDLE, handleUpdateCandle);

    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.UPDATE_CANDLE, handleUpdateCandle);
    };
  }, [chartProps.symbol, chartInterval]);

  const getMarks = async (
    symbolInfo: LibrarySymbolInfo,
    from: number,
    to: number,
    onDataCallback: GetMarksCallback<Mark>,
    resolution: ResolutionString
  ) => {
    console.log("getMarks", {
      from,
      to,
      resolution,
    });
    const marks: Mark[] = [];

    return onDataCallback(marks);
  };

  const datafeed: IBasicDataFeed = useMemo(
    () => ({
      onReady: (callback: any) => {
        setTimeout(() => callback(DATAFEED_CONFIGURATION));
      },
      searchSymbols: () => {},
      resolveSymbol: async (
        symbolName: string,
        onSymbolResolvedCallback: ResolveCallback
      ) => {
        const symbolInfo: LibrarySymbolInfo = {
          ticker: symbolName,
          name: symbolName,
          description: symbolName,
          pricescale: 10 ** 10, // need change precision
          volume_precision: 10, // need change precision
          minmov: 1,
          minmove2: 4,
          exchange: "vdax.io",
          listed_exchange: "",
          session: "24x7",
          has_intraday: true,
          has_daily: true,
          format: "price",
          has_seconds: true,
          seconds_multipliers: ["1"],
          has_weekly_and_monthly: false,
          intraday_multipliers: DATAFEED_CONFIGURATION.intraday_multipliers,
          timezone: getClientTimezone(),
          type: SYMBOL_TYPE.bitcoin,
          supported_resolutions:
            DATAFEED_CONFIGURATION.supported_resolutions as ResolutionString[],
        };
        onSymbolResolvedCallback(symbolInfo);
      },
      getBars,
      getMarks,
      subscribeBars,
      unsubscribeBars: () => {},
    }),
    [chartProps.symbol, getBars, getMarks, subscribeBars]
  );

  useEffect(() => {
    if (!chartProps?.symbol) return;
    setIsChartReady(false);

    const tvWidget = new widget({
      symbol: chartProps?.symbol?.toUpperCase(),
      theme: "Dark" as ThemeName,
      datafeed: datafeed,
      interval: chartInterval as ResolutionString,
      container: chartContainerRef.current,
      library_path: DEFAULT_LIBRARY_PATH,

      locale: "en",
      disabled_features: DISABLED_FEATURES,
      enabled_features: ENABLED_FEATURES,
      client_id: DEFAULT_CLIENT_ID,
      user_id: DEFAULT_USER_ID,
      fullscreen: chartProps.fullscreen || false,
      autosize: chartProps.autosize || true,
      custom_css_url: "/tradingview.css?id=211120241",
      timezone: getClientTimezone(),
      time_frames: [
        {
          text: "3M",
          resolution: "60" as ResolutionString,
          description: "3 months in 1 hour intervals",
        },
        {
          text: "1M",
          resolution: "30" as ResolutionString,
          description: "1 month in 30 minutes intervals",
        },
        {
          text: "5D",
          resolution: "5" as ResolutionString,
          description: "5 days in 5 minutes intervals",
        },
        {
          text: "1D",
          resolution: "1" as ResolutionString,
          description: "1 day in 1 minute intervals",
        },
      ],
      overrides: {
        "scalesProperties.fontSize": 13,
        "mainSeriesProperties.styledText.fontSize": 15, // size text
        volumePaneSize: "small",
        "paneProperties.legendProperties.showSeriesOHLC": false,
        "mainSeriesProperties.candleStyle.upColor": "#569781",
        "mainSeriesProperties.candleStyle.downColor": "#d13845",
        "mainSeriesProperties.candleStyle.drawWick": true,
        "mainSeriesProperties.candleStyle.drawBorder": true,
        "mainSeriesProperties.candleStyle.borderColor": "#378658",
        "mainSeriesProperties.candleStyle.borderUpColor": "#16C782",
        "mainSeriesProperties.candleStyle.borderDownColor": "#EA3943",
        "mainSeriesProperties.candleStyle.wickUpColor": "#16C782",
        "mainSeriesProperties.candleStyle.wickDownColor": "#EA3943",
        "mainSeriesProperties.volCandlesStyle.height": 400,
        "paneProperties.backgroundGradientStartColor": "#151618",
        "paneProperties.backgroundGradientEndColor": "#151618",
      },
      studies_overrides: {
        "volume.volume.transparency": 50, // Set transparency of volume bars
      },
      custom_formatters: {
        priceFormatterFactory: (symbolInfo: any, minTick: any) => {
          return priceFormatterFactory(symbolInfo, minTick);
        },
      },
    } as ChartingLibraryWidgetOptions);
    // tvWidget.setCSSCustomProperty("--tv-color-pane-background", "#151618");
    tradingViewChart.current = tvWidget;
    onChartReady().then();

    return () => {
      tvWidget?.remove();
    };
  }, [chartInterval, chartProps.symbol]);

  const onChartReady = useCallback(async () => {
    try {
      await new Promise<void>((resolve) => {
        tradingViewChart.current.onChartReady(() => {
          setIsChartReady(true);
          resolve();

          const activeChart = getActiveChart();
          if (activeChart) {
            activeChart
              .onIntervalChanged()
              .subscribe(null, (newInterval: any) => {
                setChartInterval(newInterval as ResolutionString);
                console.log("Chart interval changed to:", newInterval);
              });
          }
        });
      });
    } catch (error) {
      console.error("Error in onChartReady:", error);
    }
  }, [isMobile]);

  return (
    <div className="h-full">
      <div
        style={{
          height: `${"calc(100%)"}`,
        }}
        ref={chartContainerRef}
        className={"TVChartContainer"}
      />
    </div>
  );
});
TradingView.displayName = "TradingView";
export default TradingView;
